{"name": "swagger-ui-express", "version": "5.0.1", "description": "Swagger UI Express", "main": "./index.js", "files": ["package.json", "index.js", "indexTemplate.html.tpl", "swagger-ui-init.js.tpl", "LICENSE"], "scripts": {"test": "./node_modules/.bin/mocha test/*.spec.js", "coverage:report": "nyc ./node_modules/.bin/mocha test/* || exit 0", "coverage:badge": "npm run coverage:report && istanbul-badges-readme --coverageDir='./coverage'", "test-app": "node ./test/testapp/run.js "}, "keywords": ["swagger", "express", "ui", "json", "documentation"], "private": false, "engines": {"node": ">= v0.10.32"}, "devDependencies": {"es6-shim": "0.35.8", "express": "^4.19.2", "istanbul-badges-readme": "^1.9.0", "mocha": "^10.4.0", "nyc": "^15.1.0", "puppeteer": "22.8.2"}, "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "repository": {"type": "git", "url": "**************:scottie1984/swagger-ui-express.git"}, "license": "MIT", "readmeFilename": "README.md", "bugs": {"url": "https://github.com/scottie1984/swagger-ui-express/issues"}, "homepage": "https://github.com/scottie1984/swagger-ui-express", "dependencies": {"swagger-ui-dist": ">=5.0.0"}, "peerDependencies": {"express": ">=4.0.0 || >=5.0.0-beta"}}