Change Log
====================================================================================================
All notable changes will be documented in this file.
OpenAPI Schemas adheres to [Semantic Versioning](http://semver.org/).


[v2.0.0](https://github.com/APIDevTools/openapi-schemas/tree/v2.0.0) (2020-03-10)
----------------------------------------------------------------------------------------------------

- Moved OpenAPI Schemas to the [@APIDevTools scope](https://www.npmjs.com/org/apidevtools) on NPM

- The "openapi-schemas" NPM package is now just a wrapper around the scoped "@apidevtools/openapi-schemas" package

[Full Changelog](https://github.com/APIDevTools/openapi-schemas/compare/v1.0.3...v2.0.0)


[v1.0.0](https://github.com/APIDevTools/openapi-schemas/tree/v1.0.0) (2019-06-22)
----------------------------------------------------------------------------------------------------

Initial release 🎉
