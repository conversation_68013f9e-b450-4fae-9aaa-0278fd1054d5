declare interface FieldPacket {
  constructor: {
    name: 'FieldPacket';
  };
  catalog: string;
  charsetNr?: number;
  db?: string;
  schema?: string;
  characterSet?: number;
  decimals: number;
  default?: any;
  flags: number | string[];
  length?: number;
  name: string;
  orgName: string;
  orgTable: string;
  protocol41?: boolean;
  table: string;
  type?: number;
  columnType?: number;
  zerofill?: boolean;
  typeName?: string;
  encoding?: string;
  columnLength?: number;
}

export { FieldPacket };
