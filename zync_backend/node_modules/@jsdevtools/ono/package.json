{"name": "@jsdevtools/ono", "version": "7.1.3", "description": "Throw better errors.", "keywords": ["throw", "error", "errors", "exception", "printf", "format", "wrap", "inner", "original", "stack", "properties"], "author": {"name": "<PERSON>", "url": "https://jamesmessinger.com"}, "license": "MIT", "homepage": "https://jstools.dev/ono", "repository": {"type": "git", "url": "https://github.com/JS-DevTools/ono.git"}, "main": "cjs/index.js", "module": "esm/index.js", "typings": "esm/index.d.ts", "browser": {"./cjs/isomorphic.node.js": "./cjs/isomorphic.browser.js", "./esm/isomorphic.node.js": "./esm/isomorphic.browser.js"}, "files": ["cjs", "esm"], "scripts": {"clean": "shx rm -rf .nyc_output coverage cjs esm", "lint": "eslint src test", "build": "npm run build:cjs && npm run build:esm", "build:esm": "tsc", "build:cjs": "tsc --module commonjs --outDir cjs", "test": "npm run test:node && npm run test:typescript && npm run test:browser && npm run lint", "test:node": "mocha", "test:browser": "karma start --single-run", "test:typescript": "tsc --noEmit test/specs/typescript.spec.ts", "coverage": "npm run coverage:node && npm run coverage:browser", "coverage:node": "nyc node_modules/mocha/bin/mocha", "coverage:browser": "npm run test:browser -- --coverage", "upgrade": "npm-check -u && npm audit fix", "bump": "bump --tag --push --all", "release": "npm run upgrade && npm run clean && npm run build && npm test && npm run bump"}, "devDependencies": {"@babel/polyfill": "^7.10.4", "@jsdevtools/eslint-config": "^1.0.0", "@jsdevtools/host-environment": "^2.0.3", "@jsdevtools/karma-config": "^3.1.6", "@jsdevtools/version-bump-prompt": "^6.0.3", "@types/node": "^14.0.19", "chai": "^4.2.0", "eslint": "^7.4.0", "karma": "^5.1.0", "karma-cli": "^2.0.0", "mocha": "^8.0.1", "npm-check": "^5.9.2", "nyc": "^15.1.0", "shx": "^0.3.2", "typescript": "^3.9.6"}, "dependencies": {}}