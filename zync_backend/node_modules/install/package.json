{"author": {"name": "<PERSON>", "email": "<EMAIL>"}, "name": "install", "version": "0.13.0", "description": "Minimal JavaScript module loader", "keywords": ["modules", "require", "commonjs", "exports", "browser", "packaging", "packager", "install"], "license": "MIT", "homepage": "http://github.com/benjamn/install", "repository": {"type": "git", "url": "git://github.com/benjamn/install.git"}, "main": "install.js", "scripts": {"prepublish": "scripts/prepublish.sh", "docs": "scripts/docs.sh", "test": "mocha --reporter spec --full-trace test/run.js"}, "devDependencies": {"docco": "^0.8.0", "mocha": "^5.0.0", "reify": "^0.18.1", "terser": "^3.16.0"}, "engines": {"node": ">= 0.10"}}