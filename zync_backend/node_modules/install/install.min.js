makeInstaller=function(n){"use strict";var t=(n=n||{}).extensions||[".js",".json"],e=n.fallback,r=n.mainFields||(n.browser?["browser","main"]:["main"]),o={}.hasOwnProperty;function i(n,t){return p(n)&&m(t)&&o.call(n,t)}var u,s,c={},f=new w("/",new w("/..")),l=b(f);function a(n,t){return p(n)&&function n(t,e,r){Array.isArray(e)?(e.forEach(function(n){m(n)?t.deps[n]=t.module.id:v(n)?e=n:p(n)&&(t.stub=t.stub||{},k(n,function(n,e){t.stub[e]=n}))}),v(e)||(e=null)):v(e)||m(e)||p(e)||(e=null);e&&(t.contents=t.contents||(p(e)?{}:e),p(e)&&j(t)&&k(e,function(e,o){if(".."===o)i=t.parent;else{var i=h(t.contents,o);i||((i=t.contents[o]=new w(t.module.id.replace(/\/*$/,"/")+o,t)).options=r)}n(i,e,r)}))}(f,n,t),l}function d(n){this.id=n,this.children=[],this.childrenById={}}function h(n,t){return i(n,t)&&n[t]}function p(n){return null!==n&&"object"==typeof n}function v(n){return"function"==typeof n}function m(n){return"string"==typeof n}function y(n){return new Error("Cannot find module '"+n+"'")}function b(n){var t=n.module;function e(n){return t.require(n)}return e.extensions=g(n).slice(0),e.resolve=function(n){return t.resolve(n)},e}function w(n,t){this.parent=t=t||null,this.module=new d(n),c[n]=this,this.contents=null,this.deps={}}function x(n,t){var e=n.module;if(!i(e,"exports")){var r=n.contents;if(!r){if(n.stub)return n.stub;throw y(e.id)}if(t){e.parent=t;var o=t.children;Array.isArray(o)&&o.push(e)}r(b(n),e.exports=n.stub||{},e,n.module.id,n.parent.module.id),e.loaded=!0}var u=e.runSetters||e.runModuleSetters;return v(u)&&u.call(e),e.exports}function j(n){return n&&p(n.contents)}function k(n,t,e){Object.keys(n).forEach(function(e){t.call(this,n[e],e)},e)}function g(n){return n.options&&n.options.extensions||t}function I(n,t,e){for(;n&&!j(n);)n=n.parent;if(!n||!t||"."===t)return n;if(".."===t)return n.parent;var r=h(n.contents,t);if(e&&(!r||j(r)))for(var o=0;o<e.length;++o){var i=h(n.contents,t+e[o]);if(i&&!j(i))return i}return r}function A(n,t,e){var r=t.split("/");return r.every(function(t,o){return n=o<r.length-1?I(n,t):I(n,t,e)}),n}function E(n,t){var e=t&&t.module;n&&e&&(n.childrenById[e.id]=e)}function O(n,t,e,o){e=e||n.module;var i=g(n);for(n="/"===t.charAt(0)?A(f,t,i):"."===t.charAt(0)?A(n,t,i):function(n,t,e){for(var r;n&&!r;n=n.parent)r=j(n)&&A(n,"node_modules/"+t,e);return r}(n,t,i);j(n);){if((o=o||[]).indexOf(n)<0){o.push(n);var u,s=I(n,"package.json"),c=s&&x(s,e);if(c&&r.some(function(t){var r=c[t];if(m(r))return u=A(n,r,i)||O(n,r,e,o)})&&u){n=u,E(e,s);continue}}n=I(n,"index",i)}return n&&m(n.contents)&&(n=O(n,n.contents,e,o)),E(e,n),n}return a.fetch=function(n){throw new Error("fetch not implemented")},d.prototype.prefetch=function(n){var t=this,e=(h(c,t.id),u=u||Promise.resolve());function r(n){var t=h(c,n.id);(function(n){return n&&null===n.contents})(t)&&!t.pending&&(t.pending=!0,(s=s||{})[n.id]={module:t.module,deps:Object.keys(t.deps),options:t.options,stub:t.stub},k(t.deps,function(n,e){O(t,e)}),k(n.childrenById,r))}return u=new Promise(function(e){var o=t.resolve(n);k(t.childrenById,r),e(o)}).then(function(n){var t=s;function r(){t&&Object.keys(t).forEach(function(n){h(c,n).pending=!1})}return s=null,new Promise(function(n){n(t&&a.fetch(t))}).then(function(t){function o(){return a(t),r(),n}return e.then(o,o)},function(n){throw r(),n})})},a.Module=d,d.prototype.resolve=function(n){var t=O(c[this.id],n);if(t)return t.module.id;var r=y(n);if(e&&v(e.resolve))return e.resolve(n,this.id,r);throw r},d.prototype.require=function(n){var t=O(c[this.id],n);if(t)return x(t,this);var r=y(n);if(v(e))return e(n,this.id,r);throw r},a},"object"==typeof exports&&(exports.makeInstaller=makeInstaller);
