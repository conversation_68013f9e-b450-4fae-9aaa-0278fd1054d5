const mysql = require('mysql2/promise');
require('dotenv').config();

async function initializeAuthDatabase() {
  const startTime = Date.now();
  console.log('[initAuthDB] Starting database initialization...');
  
  // Check if required environment variables are set
  if (!process.env.DB_HOST || !process.env.DB_USER || !process.env.DB_PASSWORD || !process.env.DB_NAME) {
    console.error('❌ [initAuthDB] Missing environment variables:');
    console.error(`   DB_HOST: ${process.env.DB_HOST ? '✅ Set' : '❌ Missing'}`);
    console.error(`   DB_USER: ${process.env.DB_USER ? '✅ Set' : '❌ Missing'}`);
    console.error(`   DB_PASSWORD: ${process.env.DB_PASSWORD ? '✅ Set' : '❌ Missing'}`);
    console.error(`   DB_NAME: ${process.env.DB_NAME ? '✅ Set' : '❌ Missing'}`);
    throw new Error('Missing required database environment variables: DB_HOST, DB_USER, DB_PASSWORD, DB_NAME');
  }
  
  console.log('[initAuthDB] Environment variables validated');

  let initialConnection = null;
  let connection = null;

  try {
    // First, connect without specifying database to create it if it doesn't exist
    initialConnection = await mysql.createConnection({
      host: process.env.DB_HOST,
      user: process.env.DB_USER,
      password: process.env.DB_PASSWORD,
    });
    
    // Create database if it doesn't exist
    await initialConnection.execute(`CREATE DATABASE IF NOT EXISTS ${process.env.DB_NAME}`);
    console.log(`[initAuthDB] Database '${process.env.DB_NAME}' ready`);
    
    // Close the initial connection
    await initialConnection.end();
    
    // Create a new connection to the specific database
    connection = await mysql.createConnection({
      host: process.env.DB_HOST,
      user: process.env.DB_USER,
      password: process.env.DB_PASSWORD,
      database: process.env.DB_NAME,
    });
    
    // Create users table with all required fields
    
    await connection.execute(`
      CREATE TABLE IF NOT EXISTS users (
        id INT AUTO_INCREMENT PRIMARY KEY,
    
        -- Basic Information
        name VARCHAR(100),
        user_name VARCHAR(100),
        username VARCHAR(100) UNIQUE,
        email VARCHAR(320),
        password VARCHAR(255),
    
        -- Contact Details
        mobile_no VARCHAR(25),
        mobile_no_code VARCHAR(10),
    
        -- Authentication
        auth_provider VARCHAR(50),
    
        -- Verification Flags
        is_email_verified TINYINT(1) DEFAULT 0,
        is_mobile_verified TINYINT(1) DEFAULT 0,
    
        -- OTP Verification
        otp_code VARCHAR(10),
        otp_expire_at DATETIME,
    
        -- Profile Details
        profile_pic_url VARCHAR(255),
        bio TEXT,
        gender VARCHAR(10),
        date_of_birth DATE,
    
        -- Address Information
        country VARCHAR(50),
        state VARCHAR(100),
        city VARCHAR(100),
        zipcode VARCHAR(10),
        address TEXT,
    
        -- Professional Details
        profession VARCHAR(50),
        qualification VARCHAR(40),
        experience VARCHAR(25),
        skill VARCHAR(225),
    
        -- Account Status Flags
        is_blocked TINYINT(1) DEFAULT 0,
        is_deleted TINYINT(1) DEFAULT 0,
        isActive TINYINT(1) DEFAULT 1,
        
        -- Authentication Meta
        meta JSON DEFAULT ('{"TFARequire": false}'),
        secret JSON DEFAULT ('{}'),
    
        -- Timestamps
        createdAt TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        updatedAt TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
      )
    `);
    
    
    // Create roles table
    
    await connection.execute(`
      CREATE TABLE IF NOT EXISTS roles (
        id INT AUTO_INCREMENT PRIMARY KEY,
        name VARCHAR(255) NOT NULL,
        description TEXT,
        role_type VARCHAR(50),
        createdAt TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        is_deleted TINYINT(1) DEFAULT 0,
        is_active TINYINT(1) DEFAULT 1,
        created_by INT(11),
        updatedAt TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
      )
    `    );
    
    // Insert default roles if they don't exist
    const [existingRoles] = await connection.execute('SELECT COUNT(*) as count FROM roles');
    
    if (existingRoles[0].count === 0) {
      await connection.execute(`
        INSERT INTO roles (name, description) VALUES 
        ('user', 'Default user role'),
        ('admin', 'Administrator role')
      `);
    }
    
    // Create user_roles table (depends on both users and roles)
    
    await connection.execute(`
      CREATE TABLE IF NOT EXISTS user_roles (
        id INT AUTO_INCREMENT PRIMARY KEY,
        user_id INT(11) NOT NULL,
        role_id INT(11) NOT NULL,
        createdAt TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        updatedAt TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
        FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE,
        FOREIGN KEY (role_id) REFERENCES roles(id) ON DELETE CASCADE,
        UNIQUE KEY unique_user_role (user_id, role_id)
      )
    `);
    
    // Create sessions table for authentication (depends on users)
    
    await connection.execute(`
      CREATE TABLE IF NOT EXISTS sessions (
        id INT AUTO_INCREMENT PRIMARY KEY,
        user_id INT NOT NULL,
        token VARCHAR(255) UNIQUE NOT NULL,
        expires_at TIMESTAMP NOT NULL,
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE
      )
    `);
    
    // Update existing data to match new schema (if any existing data)
    const [existingUsers] = await connection.execute('SELECT COUNT(*) as count FROM users');
    
    if (existingUsers[0].count > 0) {
      // Set default meta and secret for existing users
      await connection.execute(`
        UPDATE users 
        SET meta = JSON_OBJECT('TFARequire', false),
            secret = JSON_OBJECT()
        WHERE meta IS NULL OR secret IS NULL
      `);

      // Assign default user role to existing users
      await connection.execute(`
        INSERT IGNORE INTO user_roles (user_id, role_id)
        SELECT u.id, r.id 
        FROM users u 
        CROSS JOIN roles r 
        WHERE r.name = 'user' 
        AND u.id NOT IN (SELECT user_id FROM user_roles)
      `);
    }

    console.log('[initAuthDB] Database initialization completed successfully');
    
  } catch (error) {
    console.error('[initAuthDB] Database initialization failed:', error.message);
    
    throw error;
  } finally {
    if (initialConnection) {
      try {
        await initialConnection.end();
      } catch (err) {
        console.error('[initAuthDB] Error closing initial connection:', err.message);
      }
    }
    
    if (connection) {
      try {
        await connection.end();
      } catch (err) {
        console.error('[initAuthDB] Error closing database connection:', err.message);
      }
    }
  }
}

module.exports = initializeAuthDatabase; 