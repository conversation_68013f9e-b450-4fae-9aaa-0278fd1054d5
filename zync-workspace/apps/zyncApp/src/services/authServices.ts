import apiController from './apiController';
import AsyncStorage from '@react-native-async-storage/async-storage';

export interface LoginCredentials {
  email: string;
  password: string;
}

export interface RegisterData {
  firstName: string;
  lastName: string;
  email: string;
  password: string;
}

export interface AuthResponse {
  user: {
    id: string;
    firstName: string;
    lastName: string;
    email: string;
    avatar?: string;
  };
  token: string;
  refreshToken: string;
}

export interface OtpVerificationData {
  email: string;
  otp: string;
}

export interface ForgotPasswordData {
  email: string;
}

export interface ResetPasswordData {
  email: string;
  otp: string;
  newPassword: string;
}

class AuthServices {
  private readonly TOKEN_KEY = 'auth_token';
  private readonly REFRESH_TOKEN_KEY = 'refresh_token';
  private readonly USER_KEY = 'user_data';

  // Login user
  public async login(credentials: LoginCredentials) {
    try {
      // Dummy credentials for development/testing
      const dummyUsers = [
        {
          email: '<EMAIL>',
          password: 'zync@123',
          user: {
            id: '1',
            firstName: 'Zync',
            lastName: 'User',
            email: '<EMAIL>',
            avatar: undefined,
          }
        },
        {
          email: '<EMAIL>',
          password: 'test123',
          user: {
            id: '2',
            firstName: 'Test',
            lastName: 'User',
            email: '<EMAIL>',
            avatar: undefined,
          }
        },
        {
          email: '<EMAIL>',
          password: 'demo123',
          user: {
            id: '3',
            firstName: 'Demo',
            lastName: 'User',
            email: '<EMAIL>',
            avatar: undefined,
          }
        }
      ];

      const dummyUser = dummyUsers.find(
        user => user.email === credentials.email && user.password === credentials.password
      );

      if (dummyUser) {
        const mockAuthResponse: AuthResponse = {
          user: dummyUser.user,
          token: `mock-jwt-token-${dummyUser.user.id}`,
          refreshToken: `mock-refresh-token-${dummyUser.user.id}`,
        };

        await this.storeAuthData(mockAuthResponse);
        apiController.setAuthToken(mockAuthResponse.token);

        return {
          success: true,
          data: mockAuthResponse,
        };
      }

      // Regular API call for other credentials
      const response = await apiController.post<AuthResponse>('/auth/login', credentials);
      
      if (response.success && response.data) {
        await this.storeAuthData(response.data);
        apiController.setAuthToken(response.data.token);
      }
      
      return response;
    } catch (error) {
      console.error('Login error:', error);
      throw error;
    }
  }

  // Register user
  public async register(userData: RegisterData) {
    try {
      const response = await apiController.post<AuthResponse>('/auth/register', userData);
      
      if (response.success && response.data) {
        await this.storeAuthData(response.data);
        apiController.setAuthToken(response.data.token);
      }
      
      return response;
    } catch (error) {
      console.error('Registration error:', error);
      throw error;
    }
  }

  // Verify OTP
  public async verifyOtp(otpData: OtpVerificationData) {
    try {
      const response = await apiController.post('/auth/verify-otp', otpData);
      return response;
    } catch (error) {
      console.error('OTP verification error:', error);
      throw error;
    }
  }

  // Resend OTP
  public async resendOtp(email: string) {
    try {
      const response = await apiController.post('/auth/resend-otp', { email });
      return response;
    } catch (error) {
      console.error('Resend OTP error:', error);
      throw error;
    }
  }

  // Forgot password
  public async forgotPassword(data: ForgotPasswordData) {
    try {
      const response = await apiController.post('/auth/forgot-password', data);
      return response;
    } catch (error) {
      console.error('Forgot password error:', error);
      throw error;
    }
  }

  // Reset password
  public async resetPassword(data: ResetPasswordData) {
    try {
      const response = await apiController.post('/auth/reset-password', data);
      return response;
    } catch (error) {
      console.error('Reset password error:', error);
      throw error;
    }
  }

  // Logout user
  public async logout() {
    try {
      await apiController.post('/auth/logout');
      await this.clearAuthData();
      apiController.removeAuthToken();
    } catch (error) {
      console.error('Logout error:', error);
      // Clear local data even if API call fails
      await this.clearAuthData();
      apiController.removeAuthToken();
    }
  }

  // Refresh token
  public async refreshToken() {
    try {
      const refreshToken = await AsyncStorage.getItem(this.REFRESH_TOKEN_KEY);
      
      if (!refreshToken) {
        throw new Error('No refresh token available');
      }

      const response = await apiController.post<AuthResponse>('/auth/refresh', {
        refreshToken,
      });

      if (response.success && response.data) {
        await this.storeAuthData(response.data);
        apiController.setAuthToken(response.data.token);
      }

      return response;
    } catch (error) {
      console.error('Token refresh error:', error);
      await this.clearAuthData();
      throw error;
    }
  }

  // Get current user
  public async getCurrentUser() {
    try {
      const userData = await AsyncStorage.getItem(this.USER_KEY);
      return userData ? JSON.parse(userData) : null;
    } catch (error) {
      console.error('Get current user error:', error);
      return null;
    }
  }

  // Check if user is authenticated
  public async isAuthenticated(): Promise<boolean> {
    try {
      const token = await AsyncStorage.getItem(this.TOKEN_KEY);
      return !!token;
    } catch (error) {
      console.error('Authentication check error:', error);
      return false;
    }
  }

  // Store authentication data
  private async storeAuthData(authData: AuthResponse) {
    try {
      await AsyncStorage.multiSet([
        [this.TOKEN_KEY, authData.token],
        [this.REFRESH_TOKEN_KEY, authData.refreshToken],
        [this.USER_KEY, JSON.stringify(authData.user)],
      ]);
    } catch (error) {
      console.error('Store auth data error:', error);
      throw error;
    }
  }

  // Clear authentication data
  private async clearAuthData() {
    try {
      await AsyncStorage.multiRemove([
        this.TOKEN_KEY,
        this.REFRESH_TOKEN_KEY,
        this.USER_KEY,
      ]);
    } catch (error) {
      console.error('Clear auth data error:', error);
      throw error;
    }
  }

  // Get stored token
  public async getStoredToken(): Promise<string | null> {
    try {
      return await AsyncStorage.getItem(this.TOKEN_KEY);
    } catch (error) {
      console.error('Get stored token error:', error);
      return null;
    }
  }
}

// Export singleton instance
export const authServices = new AuthServices();
export default authServices;
