import apiController from './apiController';

export interface User {
  id: string;
  firstName: string;
  lastName: string;
  email: string;
  phone?: string;
  bio?: string;
  avatar?: string;
  isOnline: boolean;
  lastSeen?: string;
  dateJoined: string;
}

export interface UpdateUserData {
  firstName?: string;
  lastName?: string;
  phone?: string;
  bio?: string;
}

export interface UserPreferences {
  notifications: {
    push: boolean;
    email: boolean;
    sms: boolean;
    mentions: boolean;
    groupMessages: boolean;
    channelUpdates: boolean;
  };
  privacy: {
    profileVisibility: 'public' | 'friends' | 'private';
    onlineStatus: boolean;
    readReceipts: boolean;
    lastSeen: boolean;
  };
  appearance: {
    theme: 'light' | 'dark' | 'auto';
    fontSize: 'small' | 'medium' | 'large';
    language: string;
  };
}

class UserServices {
  // Get user profile
  public async getUserProfile(userId?: string) {
    try {
      const url = userId ? `/users/${userId}` : '/users/me';
      const response = await apiController.get<User>(url);
      return response;
    } catch (error) {
      console.error('Get user profile error:', error);
      throw error;
    }
  }

  // Update user profile
  public async updateUserProfile(userData: UpdateUserData) {
    try {
      const response = await apiController.put<User>('/users/me', userData);
      return response;
    } catch (error) {
      console.error('Update user profile error:', error);
      throw error;
    }
  }

  // Upload user avatar
  public async uploadAvatar(imageFile: any) {
    try {
      const response = await apiController.uploadFile<{ avatarUrl: string }>('/users/me/avatar', imageFile);
      return response;
    } catch (error) {
      console.error('Upload avatar error:', error);
      throw error;
    }
  }

  // Delete user avatar
  public async deleteAvatar() {
    try {
      const response = await apiController.delete('/users/me/avatar');
      return response;
    } catch (error) {
      console.error('Delete avatar error:', error);
      throw error;
    }
  }

  // Get user preferences
  public async getUserPreferences() {
    try {
      const response = await apiController.get<UserPreferences>('/users/me/preferences');
      return response;
    } catch (error) {
      console.error('Get user preferences error:', error);
      throw error;
    }
  }

  // Update user preferences
  public async updateUserPreferences(preferences: Partial<UserPreferences>) {
    try {
      const response = await apiController.put<UserPreferences>('/users/me/preferences', preferences);
      return response;
    } catch (error) {
      console.error('Update user preferences error:', error);
      throw error;
    }
  }

  // Search users
  public async searchUsers(query: string, limit: number = 20) {
    try {
      const response = await apiController.get<User[]>('/users/search', {
        params: { q: query, limit },
      });
      return response;
    } catch (error) {
      console.error('Search users error:', error);
      throw error;
    }
  }

  // Get user contacts
  public async getUserContacts() {
    try {
      const response = await apiController.get<User[]>('/users/me/contacts');
      return response;
    } catch (error) {
      console.error('Get user contacts error:', error);
      throw error;
    }
  }

  // Add contact
  public async addContact(userId: string) {
    try {
      const response = await apiController.post(`/users/me/contacts/${userId}`);
      return response;
    } catch (error) {
      console.error('Add contact error:', error);
      throw error;
    }
  }

  // Remove contact
  public async removeContact(userId: string) {
    try {
      const response = await apiController.delete(`/users/me/contacts/${userId}`);
      return response;
    } catch (error) {
      console.error('Remove contact error:', error);
      throw error;
    }
  }

  // Block user
  public async blockUser(userId: string) {
    try {
      const response = await apiController.post(`/users/me/blocked/${userId}`);
      return response;
    } catch (error) {
      console.error('Block user error:', error);
      throw error;
    }
  }

  // Unblock user
  public async unblockUser(userId: string) {
    try {
      const response = await apiController.delete(`/users/me/blocked/${userId}`);
      return response;
    } catch (error) {
      console.error('Unblock user error:', error);
      throw error;
    }
  }

  // Get blocked users
  public async getBlockedUsers() {
    try {
      const response = await apiController.get<User[]>('/users/me/blocked');
      return response;
    } catch (error) {
      console.error('Get blocked users error:', error);
      throw error;
    }
  }

  // Update online status
  public async updateOnlineStatus(isOnline: boolean) {
    try {
      const response = await apiController.put('/users/me/status', { isOnline });
      return response;
    } catch (error) {
      console.error('Update online status error:', error);
      throw error;
    }
  }

  // Change password
  public async changePassword(currentPassword: string, newPassword: string) {
    try {
      const response = await apiController.put('/users/me/password', {
        currentPassword,
        newPassword,
      });
      return response;
    } catch (error) {
      console.error('Change password error:', error);
      throw error;
    }
  }

  // Delete account
  public async deleteAccount(password: string) {
    try {
      const response = await apiController.delete('/users/me', {
        data: { password },
      });
      return response;
    } catch (error) {
      console.error('Delete account error:', error);
      throw error;
    }
  }
}

// Export singleton instance
export const userServices = new UserServices();
export default userServices;
