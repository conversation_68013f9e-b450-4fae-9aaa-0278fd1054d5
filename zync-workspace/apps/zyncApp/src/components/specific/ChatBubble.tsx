import React from 'react';
import { View, Text } from 'react-native';
import { useTheme } from '../../context/ThemeContext';

interface ChatBubbleProps {
  message: string;
  isOwnMessage: boolean;
  senderName?: string;
  timestamp: string;
  className?: string;
}

const ChatBubble: React.FC<ChatBubbleProps> = ({
  message,
  isOwnMessage,
  senderName,
  timestamp,
  className = '',
}) => {
  const { theme } = useTheme();

  return (
    <View className={`
      flex-row mb-4
      ${isOwnMessage ? 'justify-end' : 'justify-start'}
      ${className}
    `.trim()}>
      <View className={`
        max-w-xs px-4 py-3 rounded-2xl
        ${isOwnMessage 
          ? `${theme.colors.primary} ml-12` 
          : `${theme.colors.surface} mr-12`
        }
      `.trim()}>
        {!isOwnMessage && senderName && (
          <Text className={`text-xs font-medium mb-1 ${theme.colors.textSecondary}`}>
            {senderName}
          </Text>
        )}
        
        <Text className={`
          text-base
          ${isOwnMessage ? 'text-white' : theme.colors.text}
        `.trim()}>
          {message}
        </Text>
        
        <Text className={`
          text-xs mt-1 text-right
          ${isOwnMessage 
            ? 'text-white text-opacity-70' 
            : theme.colors.textSecondary
          }
        `.trim()}>
          {timestamp}
        </Text>
      </View>
    </View>
  );
};

export default ChatBubble;
