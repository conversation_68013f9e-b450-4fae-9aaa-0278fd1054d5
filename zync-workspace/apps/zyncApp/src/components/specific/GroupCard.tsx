import React from 'react';
import { View, Text, TouchableOpacity, Image } from 'react-native';
import { useTheme } from '../../context/ThemeContext';

interface GroupCardProps {
  groupName: string;
  description: string;
  memberCount: number;
  lastMessage?: string;
  groupImage?: string;
  onPress: () => void;
  className?: string;
}

const GroupCard: React.FC<GroupCardProps> = ({
  groupName,
  description,
  memberCount,
  lastMessage,
  groupImage,
  onPress,
  className = '',
}) => {
  const { theme } = useTheme();

  return (
    <TouchableOpacity
      className={`
        ${theme.colors.surface}
        ${theme.colors.border}
        border rounded-lg p-4 mb-3
        ${className}
      `.trim()}
      onPress={onPress}
      activeOpacity={0.7}
    >
      <View className="flex-row items-center">
        <View className={`
          w-12 h-12 rounded-full mr-3
          ${theme.colors.primary}
          items-center justify-center
        `.trim()}>
          {groupImage ? (
            <Image 
              source={{ uri: groupImage }} 
              className="w-12 h-12 rounded-full"
              resizeMode="cover"
            />
          ) : (
            <Text className="text-white font-bold text-lg">
              {groupName.charAt(0).toUpperCase()}
            </Text>
          )}
        </View>
        
        <View className="flex-1">
          <View className="flex-row items-center justify-between mb-1">
            <Text className={`font-semibold text-lg ${theme.colors.text}`}>
              {groupName}
            </Text>
            <Text className={`text-xs ${theme.colors.textSecondary}`}>
              {memberCount} members
            </Text>
          </View>
          
          <Text className={`text-sm ${theme.colors.textSecondary} mb-1`} numberOfLines={1}>
            {description}
          </Text>
          
          {lastMessage && (
            <Text className={`text-sm ${theme.colors.textSecondary}`} numberOfLines={1}>
              {lastMessage}
            </Text>
          )}
        </View>
      </View>
    </TouchableOpacity>
  );
};

export default GroupCard;
