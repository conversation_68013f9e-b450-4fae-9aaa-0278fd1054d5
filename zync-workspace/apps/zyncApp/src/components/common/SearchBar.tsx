import React, { useState } from 'react';
import { View, TextInput, TouchableOpacity } from 'react-native';
import Ionicons from 'react-native-vector-icons/Ionicons';
import { useTheme } from '../../context/ThemeContext';

interface SearchBarProps {
  placeholder?: string;
  onSearch?: (query: string) => void;
  onClear?: () => void;
  value?: string;
  onChangeText?: (text: string) => void;
}

const SearchBar: React.FC<SearchBarProps> = ({
  placeholder = 'Search...',
  onSearch,
  onClear,
  value,
  onChangeText,
}) => {
  const { theme } = useTheme();
  const [searchQuery, setSearchQuery] = useState(value || '');

  const handleTextChange = (text: string) => {
    setSearchQuery(text);
    onChangeText?.(text);
    onSearch?.(text);
  };

  const handleClear = () => {
    setSearchQuery('');
    onChangeText?.('');
    onClear?.();
  };

  return (
    <View className={`flex-row items-center ${theme.colors.surface} border ${theme.colors.border} rounded-lg px-3 py-2 mb-4`}>
      <Ionicons 
        name="search" 
        size={20} 
        color={theme.isDark ? '#9CA3AF' : '#6B7280'} 
        style={{ marginRight: 8 }}
      />
      <TextInput
        className={`flex-1 ${theme.colors.text} text-base`}
        placeholder={placeholder}
        placeholderTextColor={theme.isDark ? '#9CA3AF' : '#6B7280'}
        value={searchQuery}
        onChangeText={handleTextChange}
        autoCapitalize="none"
        autoCorrect={false}
      />
      {searchQuery.length > 0 && (
        <TouchableOpacity onPress={handleClear} className="ml-2">
          <Ionicons 
            name="close-circle" 
            size={20} 
            color={theme.isDark ? '#9CA3AF' : '#6B7280'} 
          />
        </TouchableOpacity>
      )}
    </View>
  );
};

export default SearchBar; 