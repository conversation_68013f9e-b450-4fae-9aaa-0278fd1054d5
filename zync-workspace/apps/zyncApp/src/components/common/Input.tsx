import React, { useState } from 'react';
import { View, Text, TextInput, TouchableOpacity, TextInputProps } from 'react-native';
import { useTheme } from '../../context/ThemeContext';

interface InputProps extends TextInputProps {
  label?: string;
  error?: string;
  helperText?: string;
  leftIcon?: React.ReactNode;
  rightIcon?: React.ReactNode;
  onRightIconPress?: () => void;
  containerClassName?: string;
  inputClassName?: string;
}

const Input: React.FC<InputProps> = ({
  label,
  error,
  helperText,
  leftIcon,
  rightIcon,
  onRightIconPress,
  containerClassName = '',
  inputClassName = '',
  ...textInputProps
}) => {
  const { theme } = useTheme();
  const [isFocused, setIsFocused] = useState(false);

  const getBorderColor = () => {
    if (error) {
      return 'border-red-500';
    }
    if (isFocused) {
      return theme.isDark ? 'border-blue-400' : 'border-blue-500';
    }
    return theme.colors.border;
  };

  return (
    <View className={`${containerClassName}`}>
      {label && (
        <Text className={`text-sm font-medium mb-2 ${theme.colors.text}`}>
          {label}
        </Text>
      )}
      
      <View className={`
        flex-row items-center
        ${theme.colors.surface}
        ${getBorderColor()}
        border rounded-lg
        ${isFocused ? 'shadow-sm' : ''}
      `.trim()}>
        {leftIcon && (
          <View className="pl-3">
            {leftIcon}
          </View>
        )}
        
        <TextInput
          className={`
            flex-1 px-4 py-3
            ${theme.colors.text}
            ${inputClassName}
          `.trim()}
          placeholderTextColor={theme.isDark ? '#9CA3AF' : '#6B7280'}
          onFocus={() => setIsFocused(true)}
          onBlur={() => setIsFocused(false)}
          {...textInputProps}
        />
        
        {rightIcon && (
          <TouchableOpacity 
            className="pr-3"
            onPress={onRightIconPress}
            disabled={!onRightIconPress}
          >
            {rightIcon}
          </TouchableOpacity>
        )}
      </View>
      
      {(error || helperText) && (
        <Text className={`text-sm mt-1 ${error ? 'text-red-500' : theme.colors.textSecondary}`}>
          {error || helperText}
        </Text>
      )}
    </View>
  );
};

export default Input;
