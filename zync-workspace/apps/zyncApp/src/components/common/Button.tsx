import React from 'react';
import { TouchableOpacity, Text, ActivityIndicator } from 'react-native';
import { useTheme } from '../../context/ThemeContext';

interface ButtonProps {
  title: string;
  onPress: () => void;
  variant?: 'primary' | 'secondary' | 'outline';
  size?: 'small' | 'medium' | 'large';
  disabled?: boolean;
  loading?: boolean;
  className?: string;
}

const Button: React.FC<ButtonProps> = ({
  title,
  onPress,
  variant = 'primary',
  size = 'medium',
  disabled = false,
  loading = false,
  className = '',
}) => {
  const { theme } = useTheme();

  const getVariantClasses = () => {
    switch (variant) {
      case 'primary':
        return `${theme.colors.primary}`;
      case 'secondary':
        return `${theme.colors.secondary}`;
      case 'outline':
        return `${theme.colors.background} ${theme.colors.border} border-2`;
      default:
        return `${theme.colors.primary}`;
    }
  };

  const getSizeClasses = () => {
    switch (size) {
      case 'small':
        return 'py-2 px-4';
      case 'medium':
        return 'py-3 px-6';
      case 'large':
        return 'py-4 px-8';
      default:
        return 'py-3 px-6';
    }
  };

  const getTextClasses = () => {
    const baseClasses = 'text-center font-semibold';
    
    if (variant === 'outline') {
      return `${baseClasses} ${theme.colors.text}`;
    }
    
    return `${baseClasses} text-white`;
  };

  const getTextSize = () => {
    switch (size) {
      case 'small':
        return 'text-sm';
      case 'medium':
        return 'text-base';
      case 'large':
        return 'text-lg';
      default:
        return 'text-base';
    }
  };

  return (
    <TouchableOpacity
      className={`
        ${getVariantClasses()}
        ${getSizeClasses()}
        rounded-lg
        ${disabled || loading ? 'opacity-50' : ''}
        ${className}
      `.trim()}
      onPress={onPress}
      disabled={disabled || loading}
      activeOpacity={0.8}
    >
      {loading ? (
        <ActivityIndicator 
          color={variant === 'outline' ? (theme.isDark ? '#FFFFFF' : '#1F2937') : '#FFFFFF'} 
          size="small" 
        />
      ) : (
        <Text className={`${getTextClasses()} ${getTextSize()}`}>
          {title}
        </Text>
      )}
    </TouchableOpacity>
  );
};

export default Button;
