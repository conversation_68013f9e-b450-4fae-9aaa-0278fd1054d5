import React, { useState, useEffect } from 'react';
import { View, Text, SafeAreaView, ScrollView, TouchableOpacity, StatusBar, Dimensions, Platform } from 'react-native';
import Ionicons from 'react-native-vector-icons/Ionicons';
import { useTheme } from '../../context/ThemeContext';
import SearchBar from '../../components/common/SearchBar';

const AllChannelsScreen: React.FC = () => {
  const { theme } = useTheme();
  const [searchQuery, setSearchQuery] = useState('');
  const [headerHeight, setHeaderHeight] = useState(0);
  const [screenHeight, setScreenHeight] = useState(0);

  useEffect(() => {
    const calculateHeaderHeight = () => {
      const { height } = Dimensions.get('window');
      const statusBarHeight = Platform.OS === 'ios' ? 44 : StatusBar.currentHeight || 0;
      const headerContentHeight = 60; // Height for title and padding
      const totalHeaderHeight = statusBarHeight + headerContentHeight;
      
      setScreenHeight(height);
      setHeaderHeight(totalHeaderHeight);
    };

    calculateHeaderHeight();
    
    // Listen for orientation changes
    const subscription = Dimensions.addEventListener('change', calculateHeaderHeight);
    
    return () => subscription?.remove();
  }, []);

  const handleSearch = (query: string) => {
    setSearchQuery(query);
    // TODO: Implement search functionality
    console.log('Searching channels for:', query);
  };

  const handleCreateChannel = () => {
    // TODO: Navigate to create channel screen
    console.log('Create channel pressed');
  };

  return (
    <SafeAreaView className={`flex-1 ${theme.colors.background}`}>
      <StatusBar 
        barStyle={theme.isDark ? 'light-content' : 'dark-content'}
        backgroundColor={theme.isDark ? '#000000' : '#FFFFFF'}
      />
      {/* Dynamic Header with Create Button */}
      <View 
        className={`flex-row justify-between items-center px-6 mt-5 ${theme.colors.background}`}
        style={{ 
          height: headerHeight,
          paddingTop: Platform.OS === 'ios' ? 44 : StatusBar.currentHeight || 0,
          paddingBottom: 16
        }}
      >
        <Text className={`text-2xl font-bold ${theme.colors.text}`}>
          Channels
        </Text>
        <TouchableOpacity
          className={`px-4 py-2 rounded-lg ${theme.colors.primary} flex-row items-center justify-center`}
          onPress={handleCreateChannel}
        >
          <Ionicons name="add" size={20} color="white" />
          <Text className="text-white font-medium text-sm">Create</Text>
        </TouchableOpacity>
      </View>

      <ScrollView className="flex-1 px-6">
        <SearchBar 
          placeholder="Search channels..."
          onSearch={handleSearch}
        />
        
        <View className="flex-1 justify-center items-center py-8">
          <Text className={`text-center ${theme.colors.textSecondary} text-lg`}>
            No channels found.
          </Text>
          {searchQuery.length > 0 && (
            <Text className={`text-center ${theme.colors.textSecondary} text-sm mt-4`}>
              Searching for: "{searchQuery}"
            </Text>
          )}
        </View>
      </ScrollView>
    </SafeAreaView>
  );
};

export default AllChannelsScreen;