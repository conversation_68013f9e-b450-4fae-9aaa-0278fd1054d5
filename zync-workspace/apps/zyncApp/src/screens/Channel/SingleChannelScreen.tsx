import React from 'react';
import { View, Text, SafeAreaView } from 'react-native';
import { useTheme } from '../../context/ThemeContext';

const SingleChannelScreen: React.FC = () => {
  const { theme } = useTheme();

  return (
    <SafeAreaView className={`flex-1 ${theme.colors.background}`}>
      <View className="flex-1 justify-center items-center px-6">
        <Text className={`text-2xl font-bold ${theme.colors.text} mb-4`}>
          SingleChannelScreen
        </Text>
        <Text className={`text-center ${theme.colors.textSecondary}`}>
          This is a placeholder screen.
        </Text>
      </View>
    </SafeAreaView>
  );
};

export default SingleChannelScreen;
