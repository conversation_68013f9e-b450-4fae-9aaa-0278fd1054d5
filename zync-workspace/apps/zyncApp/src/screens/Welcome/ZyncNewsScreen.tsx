import React, { useState } from 'react';
import { View, Text, TouchableOpacity, SafeAreaView, TextInput, ScrollView, Image } from 'react-native';
import { useNavigation } from '@react-navigation/native';
import { NativeStackNavigationProp } from '@react-navigation/native-stack';
import { RootStackParamList } from '../../navigator/StackNavigator';
import { useTheme } from '../../context/ThemeContext';
import Icon from 'react-native-vector-icons/FontAwesome';
// ZYNC 


type NavigationProp = NativeStackNavigationProp<RootStackParamList>;

const ZyncNewsScreen: React.FC = () => {
  const navigation = useNavigation<NavigationProp>();
  const { theme } = useTheme();
  const [searchText, setSearchText] = useState('');
  const [commentText, setCommentText] = useState('');

  return (
    <SafeAreaView className={`flex-1 ${theme.colors.background}`}>
      {/* body */}
      <View className={`flex-1 ${theme.colors.background}`} style={{ marginTop: 80 }}>
        {/* Top Header */}
        <View className="flex-row items-center justify-between px-6 py-3">
          <TouchableOpacity>
            <Icon name="search" size={18} color={theme.isDark ? "white" : "#374151"} />
          </TouchableOpacity>
          <Text className={`text-lg font-bold ${theme.colors.text}`}>ZYNC News</Text>
          <TouchableOpacity>
            <Icon name="ellipsis-v" size={18} color={theme.isDark ? "white" : "#374151"} />
          </TouchableOpacity>
        </View>

        {/* Search Bar */}
        <View className="px-6 mb-2">
          <View className={`flex-row items-center rounded-xl px-4 py-2.5 ${theme.colors.surface}`}>
            <Icon name="search" size={14} color={theme.isDark ? "#9CA3AF" : "#6B7280"} style={{ marginRight: 8 }} />
            <TextInput
              className={`flex-1 text-sm ${theme.colors.text}`}
              placeholder="Search news"
              placeholderTextColor={theme.isDark ? "#9CA3AF" : "#6B7280"}
              value={searchText}
              onChangeText={setSearchText}
            />
          </View>
        </View>

        {/* News List and Input Area */}
        <ScrollView className="flex-1 px-6" showsVerticalScrollIndicator={false}>
          {/* News 1 */}
          <View className={`mb-6 rounded-xl overflow-hidden ${theme.colors.surface}`}>
            {/* News Header */}
            <View className={`flex-row items-center p-3 border-b ${theme.colors.border}`}>
              <View className="w-8 h-8 bg-red-500 rounded-full items-center justify-center mr-3">
                <Text className="text-white font-bold text-sm">T</Text>
              </View>
              <View className="flex-1">
                <Text className={`font-semibold text-sm ${theme.colors.text}`}>Tech News</Text>
                <Text className={`text-xs ${theme.colors.textSecondary}`}>2 hours ago</Text>
              </View>
              <TouchableOpacity>
                <Icon name="ellipsis-h" size={16} color={theme.isDark ? "#9CA3AF" : "#6B7280"} />
              </TouchableOpacity>
            </View>

            {/* News Content */}
            <View className="p-3">
              <View className={`w-full h-32 rounded-lg mb-3 ${theme.isDark ? 'bg-gray-800' : 'bg-gray-200'}`}>
                <View className="flex-1 items-center justify-center">
                  <Icon name="newspaper-o" size={32} color={theme.isDark ? "#9CA3AF" : "#6B7280"} />
                  <Text className={`text-sm mt-2 ${theme.colors.textSecondary}`}>News Image</Text>
                </View>
              </View>
              <Text className={`font-semibold text-sm mb-2 ${theme.colors.text}`}>AI Breakthrough: New Language Model Achieves Human-Level Understanding</Text>
              <Text className={`text-sm mb-3 ${theme.colors.textSecondary}`}>Scientists have developed a revolutionary AI model that demonstrates unprecedented language comprehension capabilities, marking a significant milestone in artificial intelligence research.</Text>
            </View>

            {/* News Actions */}
            <View className="flex-row items-center justify-between px-3 pb-3">
              <View className="flex-row items-center space-x-4">
                <TouchableOpacity className="flex-row items-center">
                  <Icon name="thumbs-up" size={18} color="#EF4444" />
                  <Text className={`text-sm ml-1 ${theme.colors.text}`}>2.5k</Text>
                </TouchableOpacity>
                <TouchableOpacity className="flex-row items-center">
                  <Icon name="comment" size={18} color={theme.isDark ? "#9CA3AF" : "#6B7280"} />
                  <Text className={`text-sm ml-1 ${theme.colors.text}`}>156</Text>
                </TouchableOpacity>
                <TouchableOpacity>
                  <Icon name="share" size={18} color={theme.isDark ? "#9CA3AF" : "#6B7280"} />
                </TouchableOpacity>
              </View>
              <TouchableOpacity>
                <Icon name="bookmark" size={18} color={theme.isDark ? "#9CA3AF" : "#6B7280"} />
              </TouchableOpacity>
            </View>
          </View>

 

          {/* Input Area - Now inside ScrollView */}
          <View className="py-3 mb-10">
            {/* Input Field */}
            <View className={`flex-row items-center rounded-xl px-4 py-2.5 mb-3 ${theme.colors.surface}`}>
              <View className="w-7 h-7 rounded-full mr-3 overflow-hidden">
                <Image
                  source={require('../../assets/images/auth/profile_emoji.jpeg')}
                  className="w-full h-full"
                  resizeMode="cover"
                />
              </View>
              <TextInput
                className={`flex-1 text-sm ${theme.colors.text}`}
                placeholder="Share your thoughts..."
                placeholderTextColor={theme.isDark ? "white" : "#374151"}
                value={commentText}
                onChangeText={setCommentText}
              />
            </View>

            {/* Action Buttons */}
            <View className="flex-row space-x-2">
              {/* Create News Button */}
              <TouchableOpacity className={`flex-1 rounded-xl py-2.5 flex-col items-center justify-center ${theme.colors.surface}`}>
                <Icon name="plus" size={26} color={theme.isDark ? "white" : "#374151"} style={{ marginBottom: 4 }} />
                <Text className={`font-medium text-xs ${theme.colors.text}`}>Create News</Text>
              </TouchableOpacity>

              {/* Share News Button */}
              <TouchableOpacity className={`flex-1 rounded-xl py-2.5 flex-col items-center justify-center ${theme.colors.surface}`}>
                <Icon name="share-square-o" size={26} color={theme.isDark ? "white" : "#374151"} style={{ marginBottom: 4 }} />
                <Text className={`font-medium text-xs ${theme.colors.text}`}>Share News</Text>
              </TouchableOpacity>

              {/* AI Summary Button */}
              <TouchableOpacity className={`flex-1 rounded-xl py-2.5 flex-col items-center justify-center ${theme.colors.surface}`}>
                <Icon name="lightbulb-o" size={26} color={theme.isDark ? "white" : "#374151"} style={{ marginBottom: 4 }} />
                <Text className={`text-xs ${theme.colors.text}`}>AI Summary</Text>
              </TouchableOpacity>
            </View>
          </View>

          {/* Back Link */}
          <View className="items-center py-2">
            <TouchableOpacity onPress={() => navigation.goBack()}>
              <Text className={`font-medium text-sm ${theme.colors.textSecondary}`}>← Back</Text>
            </TouchableOpacity>
          </View>

          {/* Login Link */}
          <View className="items-center py-2">
            <TouchableOpacity onPress={() => navigation.navigate('auth/login')}>
              <Text className="text-blue-500 font-medium text-sm cursor-pointer">Click here to login</Text>
            </TouchableOpacity>
          </View>
        </ScrollView>
      </View>
    </SafeAreaView>
  );
};

export default ZyncNewsScreen; 