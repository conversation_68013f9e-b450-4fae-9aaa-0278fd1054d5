import React from 'react';
import { View, Text, TouchableOpacity, SafeAreaView } from 'react-native';
import { useNavigation } from '@react-navigation/native';
import { NativeStackNavigationProp } from '@react-navigation/native-stack';
import { RootStackParamList } from '../../navigator/StackNavigator';
import { useTheme } from '../../context/ThemeContext';
// ZYNC 


type NavigationProp = NativeStackNavigationProp<RootStackParamList>;

const WelcomeScreen: React.FC = () => {
  const navigation = useNavigation<NavigationProp>();
  const { theme } = useTheme();

  return (
    <SafeAreaView className={`flex-1 ${theme.colors.background}`}>
      {/* Main Content - Centered */}
      <View className="flex-1 justify-center items-center px-8">
        {/* Title */}
        <Text className={`text-6xl font-bold ${theme.colors.text} mb-4 text-center`}>
          Welcome 
        </Text>
        <Text className={`text-5xl font-bold ${theme.colors.text} mb-4 text-center`}>
          to ZYNC
        </Text>
        
        {/* Subtitle */}
        <Text className={`text-xl ${theme.colors.textSecondary} text-center mb-8`}>
          where AI meets expression
        </Text>

        {/* Action Buttons */}
        <View className="w-2/3 space-y-4">
          {/* Get Started Button */}
          <TouchableOpacity
            className={`${theme.colors.primary} rounded-xl py-4 mb-4`}
            onPress={() => navigation.navigate('suite')}
          >
            <Text className="text-white text-center font-bold text-xl">
              Get Started
            </Text>
          </TouchableOpacity>

          {/* Log In Button */}
          <TouchableOpacity
            className={`bg-black rounded-xl py-4 mb-4 border border-white`}
            onPress={() => navigation.navigate('auth/login')}
          >
            <Text className="text-white text-center font-bold text-xl">
              Log In
            </Text>
          </TouchableOpacity>

          {/* Explore as Guest Button */}
          <TouchableOpacity
            className={`bg-black rounded-xl py-4 border border-white`}
            onPress={() => navigation.navigate('welcome')}
          >
            <Text className="text-white text-center font-bold text-xl">
              Explore as Guest
            </Text>
          </TouchableOpacity>
        </View>
      </View>
    </SafeAreaView>
  );
};

export default WelcomeScreen; 