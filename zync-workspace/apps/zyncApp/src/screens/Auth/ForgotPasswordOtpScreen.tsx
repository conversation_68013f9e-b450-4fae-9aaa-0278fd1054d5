import React, { useState } from 'react';
import { View, Text, TextInput, TouchableOpacity, SafeAreaView, ImageBackground, Dimensions, ActivityIndicator } from 'react-native';
import { useNavigation, useRoute } from '@react-navigation/native';
import { useTheme } from '../../context/ThemeContext';
import { validateOtp } from '../../utils/validators';
// ZYNC 

// Constants
const { height } = Dimensions.get('window');

// Main Forgot Password OTP Component
const ForgotPasswordOtpScreen: React.FC = () => {
  // Form data
  const [otp, setOtp] = useState('');
  
  // UI states
  const [isLoading, setIsLoading] = useState(false);
  const [isResending, setIsResending] = useState(false);
  
  // Error states
  const [otpError, setOtpError] = useState('');
  const [verificationError, setVerificationError] = useState('');
  
  // Navigation and context
  const navigation = useNavigation();
  const route = useRoute();
  const { theme } = useTheme();

  // Get email from route params (passed from ForgotPasswordScreen)
  const email = (route.params as any)?.email || 'your email';

  // Event Handlers
  // Handles OTP input changes and clears errors
  const handleOtpChange = (text: string) => {
    setOtp(text);
    if (otpError) {
      setOtpError('');
    }
  };

  // Handles the OTP verification with validation
  const handleVerifyOtp = async () => {
    // Clear previous errors
    setOtpError('');
    setVerificationError('');

    // Validate OTP
    if (!otp) {
      setOtpError('Please enter the OTP code');
      return;
    }

    if (!validateOtp(otp)) {
      setOtpError('Please enter a valid 6-digit OTP code');
      return;
    }

    // Attempt OTP verification
    setIsLoading(true);
    try {
      // Simulate OTP verification with 1.5 second delay
      await new Promise(resolve => setTimeout(resolve, 1500));
      
      // For now, simulate successful OTP verification
      // In real app, you would call the verify OTP API here
      (navigation as any).navigate('auth/reset-password', { email });
    } catch (error) {
      setVerificationError('Invalid OTP. Please try again.');
    } finally {
      setIsLoading(false);
    }
  };

  // Handles resending OTP
  const handleResendOtp = async () => {
    setIsResending(true);
    try {
      // Simulate resending OTP with 1 second delay
      await new Promise(resolve => setTimeout(resolve, 1000));
      
      // For now, simulate resending OTP
      // In real app, you would call the resend OTP API here
      setVerificationError('OTP resent to your email');
    } catch (error) {
      setVerificationError('Failed to resend OTP. Please try again.');
    } finally {
      setIsResending(false);
    }
  };

  // Render Methods
  // Renders the OTP input field with validation error display
  const renderOtpField = () => (
    <View className="mb-6">
      <TextInput
        className={`${theme.colors.surface} ${theme.colors.border} border rounded-lg px-4 py-3 ${theme.colors.text} text-center text-xl ${otpError ? 'border-red-500' : ''}`}
        placeholder="000000"
        placeholderTextColor={theme.isDark ? '#9CA3AF' : '#6B7280'}
        value={otp}
        onChangeText={handleOtpChange}
        keyboardType="numeric"
        maxLength={6}
        style={{ textAlignVertical: 'center' }}
      />
      {otpError ? (
        <Text className="text-red-500 text-sm mt-1 ml-1 text-center">{otpError}</Text>
      ) : null}
    </View>
  );

  // Renders the verify button with loading state
  const renderVerifyButton = () => (
    <TouchableOpacity
      className={`${theme.colors.primary} rounded-lg py-4 mb-4 flex-row items-center justify-center`}
      onPress={handleVerifyOtp}
      disabled={isLoading}
    >
      {isLoading ? (
        <>
          <ActivityIndicator size="small" color="white" style={{ marginRight: 8 }} />
          <Text className="text-white text-center font-semibold text-lg">
            Verifying...
          </Text>
        </>
      ) : (
        <Text className="text-white text-center font-semibold text-lg">
          Verify OTP
        </Text>
      )}
    </TouchableOpacity>
  );

  // Renders the resend OTP section
  const renderResendSection = () => (
    <View className="flex-row justify-center mb-4">
      <Text className={`${theme.colors.textSecondary} mr-2`}>
        Didn't receive the code?
      </Text>
      <TouchableOpacity onPress={handleResendOtp} disabled={isResending}>
        <Text className={`font-medium ${isResending ? 'text-gray-400' : 'text-blue-500'}`}>
          {isResending ? 'Sending...' : 'Resend'}
        </Text>
      </TouchableOpacity>
    </View>
  );

  // Main Render
  return (
    <SafeAreaView className={`flex-1 ${theme.colors.background}`}>
      {/* Background Image Section */}
      <View style={{ height: height * 0.26 }}>
        <ImageBackground
          source={require('../../assets/images/auth/auth_bg.png')}
          style={{ flex: 1 }}
          resizeMode="cover"
        />
      </View>

      {/* OTP Verification Form Container */}
      <View className={`${theme.colors.surface} rounded-t-3xl px-6 pt-8 pb-8 shadow-md`} style={{ flex: 1, marginTop: -30 }}>
        {/* Welcome Header */}
        <View className="mb-4">
          <Text className={`text-3xl font-bold text-start mb-2 ${theme.colors.text}`}>
            Verify OTP
          </Text>
          <Text className={`text-start ${theme.colors.textSecondary}`}>
            Enter the 6-digit code sent to {email}
          </Text>
        </View>

        {/* Form Fields */}
        {renderOtpField()}

        {/* Verification Error Display */}
        {verificationError ? (
          <View className="mb-4 p-3 bg-red-50 border border-red-200 rounded-lg">
            <Text className="text-red-600 text-sm text-center">{verificationError}</Text>
          </View>
        ) : null}

        {/* Verify Button */}
        {renderVerifyButton()}

        {/* Resend OTP Section */}
        {renderResendSection()}

        {/* Back to Forgot Password Link */}
        <View className="flex-row justify-center">
          <TouchableOpacity onPress={() => navigation.goBack()}>
            <Text className="text-blue-500 font-medium">
              Back to Forgot Password
            </Text>
          </TouchableOpacity>
        </View>
      </View>
    </SafeAreaView>
  );
};

export default ForgotPasswordOtpScreen;
