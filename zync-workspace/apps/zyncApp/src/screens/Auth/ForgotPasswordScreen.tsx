import React, { useState } from 'react';
import { View, Text, TextInput, TouchableOpacity, SafeAreaView, ImageBackground, Dimensions, ActivityIndicator } from 'react-native';
import { useNavigation } from '@react-navigation/native';
import { NativeStackNavigationProp } from '@react-navigation/native-stack';
import { useTheme } from '../../context/ThemeContext';
import { RootStackParamList } from '../../navigator/StackNavigator';
import { validateEmail } from '../../utils/validators';
// ZYNC 


// Constants
const { height } = Dimensions.get('window');

type NavigationProp = NativeStackNavigationProp<RootStackParamList>;

// Main Forgot Password Component
const ForgotPasswordScreen: React.FC = () => {
  // Form data
  const [email, setEmail] = useState('');
  
  // UI states
  const [isLoading, setIsLoading] = useState(false);
  
  // Error states
  const [emailError, setEmailError] = useState('');
  const [submitError, setSubmitError] = useState('');
  
  // Navigation and context
  const navigation = useNavigation<NavigationProp>();
  const { theme } = useTheme();

  // Event Handlers
  // Handles email input changes and clears errors
  const handleEmailChange = (text: string) => {
    setEmail(text);
    if (emailError) {
      setEmailError('');
    }
  };

  // Handles the forgot password form submission with validation
  const handleSubmit = async () => {
    // Clear previous errors
    setEmailError('');
    setSubmitError('');

    // Validate email
    if (!email) {
      setEmailError('Please enter your email address');
      return;
    }

    if (!validateEmail(email)) {
      setEmailError('Please enter a valid email address');
      return;
    }

    // Attempt to send reset email
    setIsLoading(true);
    try {
      // Simulate sending reset email with 1.5 second delay
      await new Promise(resolve => setTimeout(resolve, 1500));
      
      // For now, simulate sending reset email and navigate to OTP
      // In real app, you would call the forgot password API here
      navigation.navigate('auth/forgot-password-otp', { email });
    } catch (error) {
      setSubmitError('Failed to send reset email. Please try again.');
    } finally {
      setIsLoading(false);
    }
  };

  // Render Methods
  // Renders the email input field with validation error display
  const renderEmailField = () => (
    <View className="mb-6">
      <TextInput
        className={`${theme.colors.surface} ${theme.colors.border} border rounded-lg px-4 py-3 ${theme.colors.text} text-base ${emailError ? 'border-red-500' : ''}`}
        placeholder="Email"
        placeholderTextColor={theme.isDark ? '#9CA3AF' : '#6B7280'}
        value={email}
        onChangeText={handleEmailChange}
        keyboardType="email-address"
        autoCapitalize="none"
        autoCorrect={false}
        style={{ textAlignVertical: 'center' }}
      />
      {emailError ? (
        <Text className="text-red-500 text-sm mt-1 ml-1">{emailError}</Text>
      ) : null}
    </View>
  );

  // Renders the submit button with loading state
  const renderSubmitButton = () => (
    <TouchableOpacity
      className={`${theme.colors.primary} rounded-lg py-4 mb-4 flex-row items-center justify-center`}
      onPress={handleSubmit}
      disabled={isLoading}
    >
      {isLoading ? (
        <>
          <ActivityIndicator size="small" color="white" style={{ marginRight: 8 }} />
          <Text className="text-white text-center font-semibold text-lg">
            Sending...
          </Text>
        </>
      ) : (
        <Text className="text-white text-center font-semibold text-lg">
          Send Reset Email
        </Text>
      )}
    </TouchableOpacity>
  );

  // Main Render
  return (
    <SafeAreaView className={`flex-1 ${theme.colors.background}`}>
      {/* Background Image Section */}
      <View style={{ height: height * 0.26 }}>
        <ImageBackground
          source={require('../../assets/images/auth/auth_bg.png')}
          style={{ flex: 1 }}
          resizeMode="cover"
        />
      </View>

      {/* Forgot Password Form Container */}
      <View className={`${theme.colors.surface} rounded-t-3xl px-6 pt-8 pb-8 shadow-md`} style={{ flex: 1, marginTop: -30 }}>
        {/* Welcome Header */}
        <View className="mb-4">
          <Text className={`text-3xl font-bold text-start mb-2 ${theme.colors.text}`}>
            Forgot Password
          </Text>
          <Text className={`text-start ${theme.colors.textSecondary}`}>
            Enter your email to reset your password
          </Text>
        </View>

        {/* Form Fields */}
        {renderEmailField()}

        {/* Submit Error Display */}
        {submitError ? (
          <View className="mb-4 p-3 bg-red-50 border border-red-200 rounded-lg">
            <Text className="text-red-600 text-sm text-center">{submitError}</Text>
          </View>
        ) : null}

        {/* Submit Button */}
        {renderSubmitButton()}

        {/* Back to Login Link */}
        <View className="flex-row justify-center">
          <TouchableOpacity onPress={() => navigation.goBack()}>
            <Text className="text-blue-500 font-medium">
              Back to Login
            </Text>
          </TouchableOpacity>
        </View>
      </View>
    </SafeAreaView>
  );
};

export default ForgotPasswordScreen;
