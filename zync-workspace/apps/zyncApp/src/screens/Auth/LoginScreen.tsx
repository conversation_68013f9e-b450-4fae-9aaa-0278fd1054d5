import React, { useState } from 'react';
import { 
  View, 
  Text, 
  TextInput, 
  TouchableOpacity, 
  SafeAreaView, 
  ImageBackground, 
  Dimensions, 
  Image, 
  ActivityIndicator, 
  Platform 
} from 'react-native';
import { useNavigation } from '@react-navigation/native';
import { NativeStackNavigationProp } from '@react-navigation/native-stack';
import Icon from 'react-native-vector-icons/FontAwesome';
import Ionicons from 'react-native-vector-icons/Ionicons';
import { useAuth } from '../../context/AuthContext';
import { useTheme } from '../../context/ThemeContext';
import { RootStackParamList } from '../../navigator/StackNavigator';
import { validateEmail, validatePassword } from '../../utils/validators';

// Constants & Types
const { height } = Dimensions.get('window');

type NavigationProp = NativeStackNavigationProp<RootStackParamList>;

// Main LoginScreen Component
const LoginScreen: React.FC = () => {
  // State Management
  const [email, setEmail] = useState('<EMAIL>');
  const [password, setPassword] = useState('zync@123');
  const [isLoading, setIsLoading] = useState(false);
  const [showPassword, setShowPassword] = useState(false);
  const [rememberMe, setRememberMe] = useState(false);
  const [emailError, setEmailError] = useState('');
  const [passwordError, setPasswordError] = useState('');
  const [loginError, setLoginError] = useState('');

  // Hooks & Context
  const navigation = useNavigation<NavigationProp>();
  const { login } = useAuth();
  const { theme } = useTheme();

  // Event Handlers
  // Handles email input changes and clears email errors
  const handleEmailChange = (text: string) => {
    setEmail(text);
    if (emailError) {
      setEmailError('');
    }
  };

  // Handles password input changes and clears password errors
  const handlePasswordChange = (text: string) => {
    setPassword(text);
    if (passwordError) {
      setPasswordError('');
    }
  };

  // Handles the login form submission with validation
  const handleLogin = async () => {
    // Clear all previous errors
    setEmailError('');
    setPasswordError('');
    setLoginError('');

    // Validate email field
    let hasEmailError = false;
    if (!email) {
      setEmailError('Email is required');
      hasEmailError = true;
    } else if (!validateEmail(email)) {
      setEmailError('Please enter a valid email address');
      hasEmailError = true;
    }

    // Validate password field
    let hasPasswordError = false;
    if (!password) {
      setPasswordError('Password is required');
      hasPasswordError = true;
    } else {
      const passwordValidation = validatePassword(password);
      if (!passwordValidation.isValid) {
        setPasswordError(passwordValidation.errors[0]); // Show first error
        hasPasswordError = true;
      }
    }

    // Stop if validation errors exist
    if (hasEmailError || hasPasswordError) {
      return;
    }

    // Attempt login
    setIsLoading(true);
    try {
      // Simulate login process with 1.5 second delay
      await new Promise(resolve => setTimeout(resolve, 1500));
      
      const success = await login(email, password);
      if (success) {
        // Navigate to main app on successful login
        navigation.navigate('app/main');
      } else {
        setLoginError('Email or password is wrong');
      }
    } catch (error) {
      setLoginError('Login failed. Please try again.');
    } finally {
      setIsLoading(false);
    }
  };

  // Render Methods
  // Renders the email input field with validation error display
  const renderEmailField = () => (
    <View className="mb-4">
      <TextInput
        className={`${theme.colors.surface} ${theme.colors.border} border rounded-lg px-4 py-4 ${theme.colors.text} text-base ${emailError ? 'border-red-500' : ''}`}
        placeholder="Email"
        placeholderTextColor={theme.isDark ? '#9CA3AF' : '#6B7280'}
        value={email}
        onChangeText={handleEmailChange}
        keyboardType="email-address"
        autoCapitalize="none"
        autoCorrect={false}
      />
      {emailError ? (
        <Text className="text-red-500 text-sm mt-1 ml-1">{emailError}</Text>
      ) : null}
    </View>
  );

  // Renders the password input field with show/hide toggle
  const renderPasswordField = () => (
    <View className="mb-6">
      <View className="relative">
        <TextInput
          className={`${theme.colors.surface} ${theme.colors.border} border rounded-lg px-4 py-4 ${theme.colors.text} text-base ${passwordError ? 'border-red-500' : ''} pr-12`}
          placeholder="Password"
          placeholderTextColor={theme.isDark ? '#9CA3AF' : '#6B7280'}
          value={password}
          onChangeText={handlePasswordChange}
          secureTextEntry={!showPassword}
        />
        <TouchableOpacity
          className="absolute right-3 top-0 bottom-0 justify-center"
          onPress={() => setShowPassword(!showPassword)}
        >
          <Ionicons 
            name={showPassword ? "eye-off" : "eye"} 
            size={20} 
            color={theme.isDark ? '#9CA3AF' : '#6B7280'} 
          />
        </TouchableOpacity>
      </View>
      {passwordError ? (
        <Text className="text-red-500 text-sm mt-1 ml-1">{passwordError}</Text>
      ) : null}
    </View>
  );

  // Renders the login button with loading state
  const renderLoginButton = () => (
    <TouchableOpacity
      className={`${theme.colors.primary} rounded-lg py-4 mb-4 flex-row items-center justify-center`}
      onPress={handleLogin}
      disabled={isLoading}
    >
      {isLoading ? (
        <>
          <ActivityIndicator size="small" color="white" style={{ marginRight: 8 }} />
          <Text className="text-white text-center font-semibold text-lg">
            Signing In...
          </Text>
        </>
      ) : (
        <Text className="text-white text-center font-semibold text-lg">
          Login
        </Text>
      )}
    </TouchableOpacity>
  );

  // Renders social login buttons (Google, Apple, Microsoft)
  const renderSocialLoginButtons = () => (
    <View className="mb-6">
      {/* Google Login Button */}
      <View className="mb-2">
        <TouchableOpacity className={`${theme.colors.surface} border ${theme.colors.border} rounded-lg py-3 px-4 flex-row items-center justify-center`}>
          <Image 
            source={require('../../assets/images/auth/google.png')}
            style={{ width: 20, height: 20, marginRight: 8 }}
            resizeMode="contain"
          />
          <Text className={`font-medium ${theme.colors.text}`}>Continue with Google</Text>
        </TouchableOpacity>
      </View>

      {/* Apple Login Button - Only show on iOS */}
      {Platform.OS === 'ios' && (
        <View className="mb-2">
          <TouchableOpacity className={`${theme.colors.surface} border ${theme.colors.border} rounded-lg py-3 px-4 flex-row items-center justify-center`}>
            <Icon name="apple" size={20} color="#ffffff" style={{ marginRight: 8 }} />
            <Text className={`font-medium ${theme.colors.text}`}>Continue with Apple</Text>
          </TouchableOpacity>
        </View>
      )}

      {/* Microsoft Login Button */}
      <View className="mb-2">
        <TouchableOpacity className={`${theme.colors.surface} border ${theme.colors.border} rounded-lg py-3 px-4 flex-row items-center justify-center`}>
          <Image 
            source={require('../../assets/images/auth/microsoft.png')}
            style={{ width: 20, height: 20, marginRight: 8 }}
            resizeMode="contain"
          />
          <Text className={`font-medium ${theme.colors.text}`}>Continue with Microsoft Account</Text>
        </TouchableOpacity>
      </View>
    </View>
  );

  // Main Render
  return (
    <SafeAreaView className={`flex-1 ${theme.colors.background}`}>
      {/* Background Image Section */}
      <View style={{ height: height * 0.26 }}>
        <ImageBackground
          source={require('../../assets/images/auth/auth_bg.png')}
          style={{ flex: 1 }}
          resizeMode="cover"
        />
      </View>

      {/* Login Form Container */}
      <View className={`${theme.colors.surface} rounded-t-3xl px-6 pt-8 pb-8 shadow-md`} style={{ flex: 1, marginTop: -30 }}>
        {/* Welcome Header */}
        <View className="mb-4">
          <Text className={`text-3xl font-bold text-start mb-2 ${theme.colors.text}`}>
            Welcome Back!
          </Text>
        </View>

        {/* Form Fields */} v   
        {renderEmailField()}
        {renderPasswordField()}

        {/* Login Error Display */}
        {loginError ? (
          <View className="mb-4 p-3 bg-red-50 border border-red-200 rounded-lg">
            <Text className="text-red-600 text-sm text-center">{loginError}</Text>
          </View>
        ) : null}

        {/* Login Button */}
        {renderLoginButton()}

        {/* Remember Me & Forgot Password */}
        <View className="flex-row justify-between items-center mb-4">
          <View className="flex-row items-center">
            <TouchableOpacity 
              className={`w-5 h-5 border-2 rounded mr-2 items-center justify-center ${rememberMe ? 'bg-blue-500 border-blue-500' : `border-gray-400 ${theme.isDark ? 'bg-gray-700' : 'bg-white'}`}`}
              onPress={() => setRememberMe(!rememberMe)}
            >
              {rememberMe && (
                <Ionicons name="checkmark" size={14} color="white" />
              )}
            </TouchableOpacity>
            <Text className={`text-sm ${theme.colors.text}`}>Remember Me</Text>
          </View>
          <TouchableOpacity onPress={() => navigation.navigate('auth/forgot-password')}>
            <Text className="text-red-500 text-sm font-medium">
              Forgot Your Password?
            </Text>
          </TouchableOpacity>
        </View>

        {/* Social Login Options */}
        {renderSocialLoginButtons()}

        {/* Sign Up Link */}
        <View className="flex-row justify-center">
          <Text className={theme.colors.textSecondary}>
            Don't have an account?{' '}
          </Text>
          <TouchableOpacity onPress={() => navigation.navigate('auth/register')}>
            <Text className="text-blue-500 font-medium">
              Sign Up
            </Text>
          </TouchableOpacity>
        </View>
      </View>
    </SafeAreaView>
  );
};

export default LoginScreen;
