const { getDefaultConfig } = require('@react-native/metro-config');
const { withNativeWind } = require('nativewind/metro');
const path = require('path');

/**
 * Metro configuration for workspace
 */
const config = getDefaultConfig(__dirname);

// Set the project root to the ZyncApp directory
config.projectRoot = path.resolve(__dirname, 'ZyncApp');

module.exports = withNativeWind(config, { 
  input: './ZyncApp/global.css' 
});