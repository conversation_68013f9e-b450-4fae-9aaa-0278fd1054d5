const { getDefaultConfig } = require('@react-native/metro-config');
const { withNativeWind } = require('nativewind/metro');
const path = require('path');

/**
 * Metro configuration for monorepo
 * https://reactnative.dev/docs/metro
 *
 * @type {import('@react-native/metro-config').MetroConfig}
 */
const config = getDefaultConfig(__dirname);

// Configure for monorepo structure
config.watchFolders = [
  // Watch the entire workspace
  __dirname,
  // Watch node_modules at root
  path.resolve(__dirname, 'node_modules'),
];

config.resolver.nodeModulesPaths = [
  path.resolve(__dirname, 'node_modules'),
];

// Set the project root to the zyncApp directory
config.projectRoot = path.resolve(__dirname, 'apps/zyncApp');

module.exports = withNativeWind(config, { 
  input: './apps/zyncApp/global.css' 
});
